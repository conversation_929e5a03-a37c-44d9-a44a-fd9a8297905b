<script lang="ts" setup>
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import router from "@/router";
import { RouteName } from "@/constants/route_name";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import LimitedOffer from "@/components/LimitedOffer/LimitedOffer.vue";
import { useUserSubscriptionsTariffs } from "@/composable";

const { isActiveSubscriptionsExperimentRetentionUsers } =
  useUserSubscriptionsTariffs();

if (!isActiveSubscriptionsExperimentRetentionUsers.value) {
  // router.push({ name: RouteName.NOT_FOUND });
}

const onClose = () => {
  router.push({ name: RouteName.DASHBOARD });
};
</script>

<template>
  <div class="select-tariff-view">
    <UIFullScreenModal
      :is-open="true"
      @close="onClose">
      <template #leftButtons>
        <DynamicIcon
          name="pst-logo"
          class="w-[138px] md:w-[173px] h-auto md:ml-[calc(50%-93px)]"
          path="./" />
      </template>
      <template #content>
        <LimitedOffer />
      </template>
    </UIFullScreenModal>
  </div>
</template>
